// Firestore database models

// User Profile Model
export interface UserProfile {
  uid: string;
  displayName: string;
  email: string;
  photoURL?: string;
  phoneNumber?: string;
  createdAt: Date | string;
  updatedAt: Date | string;
  loyaltyPoints: number;
  preferences?: UserPreferences;
}

// Admin Model
export interface Admin {
  uid: string;
  email: string;
  displayName?: string;
  photoURL?: string;
  createdAt: Date | string;
  updatedAt: Date | string;
}

// User Preferences
export interface UserPreferences {
  language: string;
  darkMode: boolean;
  emailNotifications: boolean;
  pushNotifications: boolean;
}

// Order Model
export interface Order {
  id: string;
  userId: string;
  items: OrderItem[];
  status: OrderStatus;
  subtotal: number;
  deliveryFee: number;
  total: number;
  paymentMethod: PaymentMethod;
  deliveryType?: DeliveryZoneType;
  tableNumber?: string;
  deliveryAddress?: string;
  deliveryZoneId?: string;
  createdAt: Date | string;
  updatedAt: Date | string;
  specialInstructions?: string;
  // Cancellation fields
  cancellationData?: OrderCancellationData;
  cancellationHistory?: OrderCancellationAuditEntry[];
  editHistory?: OrderEditAuditEntry[];
}

// Order Cancellation Data
export interface OrderCancellationData {
  reason: string;
  cancelledBy: 'customer' | 'admin';
  cancelledByUserId: string;
  cancelledByEmail?: string;
  cancelledAt: Date | string;
  refundAmount?: number;
  refundMethod?: string;
  notes?: string;
}

// Order Cancellation Audit Entry
export interface OrderCancellationAuditEntry {
  timestamp: Date | string;
  userId: string;
  userEmail: string;
  userType: 'customer' | 'admin';
  action: 'order_cancelled';
  reason: string;
  refundAmount?: number;
  refundMethod?: string;
  notes?: string;
}

// Order Edit Audit Entry
export interface OrderEditAuditEntry {
  timestamp: Date | string;
  adminId: string;
  adminEmail: string;
  action: 'item_added' | 'item_removed' | 'item_modified' | 'delivery_updated' | 'instructions_updated';
  details: string;
  oldValue?: any;
  newValue?: any;
}

// Order Item
export interface OrderItem {
  id: string;
  name: string;
  price: number;
  quantity: number;
  options?: OrderItemOption[];
}

// Order Item Option
export interface OrderItemOption {
  name: string;
  value: string;
  priceAdjustment: number;
}

// Order Status
export enum OrderStatus {
  ORDER_PLACED = 'order_placed',
  PREPARING = 'preparing',
  READY_FOR_PICKUP = 'ready_for_pickup',
  OUT_FOR_DELIVERY = 'out_for_delivery',
  DELIVERED = 'delivered',
  CANCELLED = 'cancelled'
}

// Payment Method
export enum PaymentMethod {
  CREDIT_CARD = 'credit_card',
  DEBIT_CARD = 'debit_card',
  CASH = 'cash',
  GIFT_CARD = 'gift_card',
  LOYALTY_POINTS = 'loyalty_points'
}

// Address Model
export interface Address {
  id: string;
  userId: string;
  name: string;
  addressLine1: string;
  addressLine2?: string;
  city: string;
  state: string;
  postalCode: string;
  country: string;
  isDefault: boolean;
  createdAt: Date | string;
  updatedAt: Date | string;
}

// Gift Card Model
export interface GiftCard {
  id: string;
  userId: string;
  code: string;
  initialBalance: number;
  currentBalance: number;
  expiryDate: Date | string;
  isActive: boolean;
  createdAt: Date | string;
  updatedAt: Date | string;
}

// Review Model
export interface Review {
  id: string;
  userId: string;
  orderId?: string;
  title?: string;
  rating: number;
  comment?: string;
  createdAt: Date | string;
  updatedAt: Date | string;
}

// Category Model
export interface Category {
  id: string;
  userId: string;
  name: string;
  name_ar?: string; // Arabic translation for category name
  icon: string;
  description?: string;
  description_ar?: string; // Arabic translation for category description
  itemCount: number;
  availableFrom?: string;
  availableTo?: string;
  isActive: boolean;
  isVisible: boolean;
  isFeatured: boolean;
  displayOrder: number;
  createdAt: Date | string;
  updatedAt: Date | string;
}

// Stock Status
export enum StockStatus {
  IN_STOCK = 'in_stock',
  LOW_STOCK = 'low_stock',
  OUT_OF_STOCK = 'out_of_stock'
}

// Menu Item Model
export interface MenuItem {
  id: string;
  userId: string;
  title: string;
  title_ar?: string; // Arabic translation for item name
  description: string;
  description_ar?: string; // Arabic translation for item description
  price: number;
  categoryId: string;
  image: string;
  stockStatus: StockStatus;
  stockQuantity: number;
  prepTime?: number;
  caffeine?: string;
  caffeine_ar?: string; // Arabic translation for caffeine content
  ingredients?: string;
  ingredients_ar?: string; // Arabic translation for ingredients
  allergens?: string;
  allergens_ar?: string; // Arabic translation for allergens
  isActive: boolean;
  isFeatured: boolean;
  isAvailableForDelivery: boolean;
  createdAt: Date | string;
  updatedAt: Date | string;
}

// Delivery Zone Type
export enum DeliveryZoneType {
  PICK_UP = 'pick_up',
  DELIVERY = 'delivery',
  IN_HOUSE_TABLES = 'in_house_tables'
}

// Delivery Zone Model
export interface DeliveryZone {
  id: string;
  userId: string;
  name: string;
  type: DeliveryZoneType;
  description?: string;
  isActive: boolean;
  // For DELIVERY type
  radius?: number;
  deliveryFee?: number;
  minOrderAmount?: number;
  estimatedDeliveryTime?: number;
  // For IN_HOUSE_TABLES type
  tableNumbers?: string[];
  // Common fields
  createdAt: Date | string;
  updatedAt: Date | string;
} 